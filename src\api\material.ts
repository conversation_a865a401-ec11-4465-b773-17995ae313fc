import axios from 'axios';
import type { HttpResponse } from './interceptor';

export interface MaterialItem {
  id: number;
  sequence: number;
  name: string;
  type: string;
  size: string;
  createTime: string;
  url?: string;
  thumbnail?: string;
}

export interface MaterialListParams {
  page?: number;
  size?: number;
  type?: string;
  name?: string;
}

export interface MaterialListResponse {
  list: MaterialItem[];
  total: number;
  page: number;
  size: number;
}

// 获取素材列表
export function getMaterialList(params: MaterialListParams) {
  return axios.get<HttpResponse<MaterialListResponse>>('/api/material/list', {
    params,
  });
}

// 删除单个素材
export function deleteMaterial(id: number) {
  return axios.delete<HttpResponse<null>>(`/api/material/${id}`);
}

// 批量删除素材
export function batchDeleteMaterial(ids: number[]) {
  return axios.delete<HttpResponse<null>>('/api/material/batch', {
    data: { ids },
  });
}

// 上传素材
export function uploadMaterial(file: File, type?: string) {
  const formData = new FormData();
  formData.append('file', file);
  if (type) {
    formData.append('type', type);
  }
  
  return axios.post<HttpResponse<MaterialItem>>('/api/material/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 更新素材信息
export function updateMaterial(id: number, data: Partial<MaterialItem>) {
  return axios.put<HttpResponse<MaterialItem>>(`/api/material/${id}`, data);
}