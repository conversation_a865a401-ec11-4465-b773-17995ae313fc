<template>
  <a-modal v-model:visible="visible" :footer="false">
    <template #title> {{t('campaign.canvas.addTitle')}} </template>
    <div class="modal-content">
      <div class="modal-item" @click="addCamapign('AUDIENCE')">
        <div class="modal-tag"><icon-user /></div>
        <div>人群包活动</div>
      </div>
      <!-- <div class="modal-item" @click="addCamapign('EVENT')">
        <div class="modal-tag"><icon-phone /></div>事件活动
      </div> -->
      <div class="modal-item" @click="addCamapign('FLOW')">
        <div class="modal-tag"><icon-branch /></div>{{t('campaign.flow.campaign')}}
      </div>
    </div>

    <div class="modal-content template-list">
      <div v-for="item in dataList" :key="item.id" class="modal-item" @click="addCamapign('FLOW', item.id)">
        <div class="modal-tag">
          <icon-plus />
        </div>
        {{ item.name }}
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { getTemplateList } from "@/api/campaign";

const {
      proxy: { t }
    } = getCurrentInstance();

const emit = defineEmits(["onChangeItem"]);

const visible = ref(false);
const dataList = ref([]);
getTemplateList().then((res) => {
  dataList.value = res;
});

// 点击数据
const addCamapign = (type, id) => {
  emit("onChangeItem", type, id);
  visible.value = false;
};
defineExpose({
  visible
});
</script>

<style lang="less" scoped>
.modal-content {
  display: flex;
  align-items: center;

  .modal-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
    margin-right: 7px;
  }

  .arco-icon {
    color: #999;
    font-size: 40px;
  }

  .modal-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 115px;
    height: 160px;
    margin-bottom: 5px;
    border: 1px dashed #999;
    background-color: #efefef;

    &:hover {
      transition: all 0.2s;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }
  }
}

.template-list{
  padding: 10px;
}
</style>
